/**
 * Test script for the complete LettersBot solver on the live Letters game website
 * 
 * This script runs the full solver to find and play optimal words on the live game.
 */

import { chromium, type Browser, type Page } from '@playwright/test';
import { scrapeBoard, injectHelperFunctions } from '../lib/solver/scraping';
import { bestWords } from '../lib/bestWords';
import { playWord } from '../lib/solver/gameInteraction';

async function main() {
	console.log('🤖 Testing Complete LettersBot Solver on Live Website');
	console.log('===================================================\n');

	let browser: Browser | null = null;
	let page: Page | null = null;

	try {
		// Launch browser
		console.log('1. Launching browser...');
		browser = await chromium.launch({ 
			headless: false, // Keep visible for debugging
			slowMo: 800 // Slow down actions for visibility
		});
		
		page = await browser.newPage();
		page.setDefaultTimeout(30000);

		// Navigate to the Letters game
		console.log('2. Navigating to Letters game...');
		await page.goto('https://play.thelettersgame.com/', { waitUntil: 'networkidle' });
		await page.waitForTimeout(3000);

		console.log('3. Looking for "Play on Web" button...');
		const playButton = await page.$('button:has-text("Play on Web")');
		if (playButton) {
			console.log('   ✅ Found "Play on Web" button, clicking...');
			await playButton.click();
			await page.waitForTimeout(5000);
		}

		// Inject helper functions
		console.log('4. Injecting helper functions...');
		await injectHelperFunctions(page);

		// Take initial screenshot
		await page.screenshot({ path: 'solver-initial-state.png', fullPage: true });
		console.log('   📸 Initial state screenshot saved');

		// Run the complete solver
		console.log('5. Running complete solver...');
		await runCompleteSolver(page);

		console.log('\n🎉 Complete solver test finished!');
		
	} catch (error) {
		console.error('❌ Error during testing:', error);
		console.error('Stack trace:', (error as Error).stack);
		
		if (page) {
			try {
				await page.screenshot({ path: 'solver-error.png', fullPage: true });
				console.log('   📸 Error screenshot saved');
			} catch (screenshotError) {
				console.error('Failed to take error screenshot:', screenshotError);
			}
		}
	} finally {
		if (browser) {
			console.log('\n6. Keeping browser open for inspection...');
			console.log('   Press Ctrl+C to close when done inspecting');
			// Keep browser open for manual inspection
			await new Promise(resolve => setTimeout(resolve, 120000)); // Wait 2 minutes
			await browser.close();
		}
	}
}

async function runCompleteSolver(page: Page): Promise<void> {
	let moveCount = 0;
	const maxMoves = 3; // Limit to 3 moves for testing
	
	console.log(`   🎯 Running solver for up to ${maxMoves} moves...\n`);

	while (moveCount < maxMoves) {
		console.log(`   === MOVE ${moveCount + 1} ===`);
		
		try {
			// Scrape current board state
			console.log(`   📊 Scraping board state...`);
			const board = await scrapeBoard(page);
			
			console.log(`   🎲 Current board:`);
			for (let row = 0; row < 5; row++) {
				const rowStr = board.tiles[row].map(tile => 
					`${tile.letter}${tile.letterMult > 1 ? `(L${tile.letterMult})` : ''}${tile.wordMult > 1 ? `(W${tile.wordMult})` : ''}`
				).join(' ');
				console.log(`      ${rowStr}`);
			}

			// Find best words (limit to top 3 for speed)
			console.log(`   🧠 Finding best words...`);
			const words = bestWords(board, 3);
			
			if (words.length === 0) {
				console.log(`   ⚠️  No valid words found. Game may be complete.`);
				break;
			}

			console.log(`   📝 Found ${words.length} possible words:`);
			words.forEach((word, index) => {
				console.log(`      ${index + 1}. ${word.letters} (score: ${word.score}) at ${word.positions.map(p => `[${p[0]},${p[1]}]`).join('-')}`);
			});

			// Play the best word
			const bestWord = words[0];
			console.log(`\n   🎮 Playing best word: ${bestWord.letters} (score: ${bestWord.score})`);
			
			// Take screenshot before move
			await page.screenshot({ path: `solver-before-move-${moveCount + 1}.png`, fullPage: true });
			
			// Play the word
			await playWord(page, bestWord.positions);
			
			// Wait for game to process
			await page.waitForTimeout(3000);
			
			// Take screenshot after move
			await page.screenshot({ path: `solver-after-move-${moveCount + 1}.png`, fullPage: true });
			
			console.log(`   ✅ Successfully played word: ${bestWord.letters}`);
			
			moveCount++;
			
			// Wait before next move
			if (moveCount < maxMoves) {
				console.log(`   ⏳ Waiting before next move...\n`);
				await page.waitForTimeout(2000);
			}
			
		} catch (error) {
			console.error(`   ❌ Error on move ${moveCount + 1}:`, error);
			
			// Take error screenshot
			await page.screenshot({ path: `solver-error-move-${moveCount + 1}.png`, fullPage: true });
			
			// Try to continue with next move
			moveCount++;
			if (moveCount < maxMoves) {
				console.log(`   🔄 Attempting to continue with next move...\n`);
				await page.waitForTimeout(3000);
			}
		}
	}
	
	// Take final screenshot
	await page.screenshot({ path: 'solver-final-state.png', fullPage: true });
	console.log(`\n   📸 Final state screenshot saved`);
	console.log(`   🏁 Completed ${moveCount} moves`);
}

// Run the test
console.log('🚀 Starting complete solver test...');
main().catch((error) => {
	console.error('💥 Script failed with error:', error);
	process.exit(1);
});
