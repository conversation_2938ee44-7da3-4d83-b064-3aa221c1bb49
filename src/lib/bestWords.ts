// bestWords.ts
//------------------------------------------------------------
// Public entry point:
//     const words = bestWords(board, K);
//
// Dependencies:  playBoardTypes.ts (Tile, Board, Word)
//                Hungarian.ts      (any O(n3) assignment impl)
//
// Binary dictionary is memory-mapped once (Uint8Array view).
//------------------------------------------------------------
import { readFileSync } from 'node:fs';
import { Tile } from './models/Tile';
import { Board } from './models/Board';
import { Word } from './models/Word';
import { solveAssignment } from './solver/hungarian';
import { join } from 'path';

const DICT_BIN = join(process.cwd(), 'src/lib/dict.bin');
const dictBytes = readFileSync(DICT_BIN); // < 2 MB, ok synchronously
const dv = new DataView(dictBytes.buffer, dictBytes.byteOffset);
const textDec = new TextDecoder();

// ---- letter score map must match compile script ----------------------------
const LETTER_SCORES: number[] = Array(26).fill(0);
Object.assign(LETTER_SCORES, {
	0: 1,
	1: 4,
	2: 4,
	3: 2,
	4: 1,
	5: 4,
	6: 2,
	7: 4,
	8: 1,
	9: 10,
	10: 5,
	11: 1,
	12: 4,
	13: 2,
	14: 1,
	15: 3,
	16: 10,
	17: 1,
	18: 1,
	19: 1,
	20: 2,
	21: 5,
	22: 4,
	23: 8,
	24: 3,
	25: 10
});

// ---- loader creates a typed view: offsets[] into the original buffer --------
interface DictEntryMeta {
	off: number; // offset of word UTF-8 blob
	len: number; // byte length of UTF-8
	letterScoreSum: number;
	wordLen: number;
	hist: Uint8Array; // 26
}
const ENTRIES: DictEntryMeta[] = (() => {
	let o = 0;
	const nEntries = dv.getUint32(o, true);
	o += 4;
	const out: DictEntryMeta[] = new Array(nEntries);

	for (let idx = 0; idx < nEntries; idx++) {
		const wordBytes = dv.getUint16(o, true);
		o += 2;
		const score = dv.getUint16(o, true);
		o += 2;
		const wlen = dv.getUint8(o);
		o += 1;
		const hist = new Uint8Array(dictBytes.buffer, dictBytes.byteOffset + o, 26);
		o += 26;

		out[idx] = { off: o, len: wordBytes, letterScoreSum: score, wordLen: wlen, hist };
		o += wordBytes;
	}
	return out;
})();

// ---- helper: board summary --------------------------------------------------
interface LetterBag {
	freq: Uint8Array;
	tiles: Tile[][];
} // tiles[L] = list of Tile with that letter
function summariseBoard(board: Board): LetterBag {
	const freq = new Uint8Array(26);
	const tiles: Tile[][] = Array.from({ length: 26 }, (_) => []);
	for (const row of board.tiles)
		for (const t of row) {
			const idx = t.letter.charCodeAt(0) - 65;
			freq[idx]++;
			tiles[idx].push(t);
		}
	return { freq, tiles };
}

// ---- scoring assignment (Hungarian) ---------------------------------------
function bestPlacementScore(word: string, tilesBag: Tile[][], letterScoreSum: number): number {
	// Build cost matrix = -(final letter score), because Hungarian gives min cost.
	// n = word.length ≤ 12
	const n = word.length;
	const M = Array.from<number[], number>({ length: n }, () => new Array(n).fill(0));

	// For each occurrence of letter i in word, enumerate candidate tiles j
	const letterCounts: Record<string, number> = {};
	for (let i = 0; i < n; i++) {
		const ch = word[i];
		letterCounts[ch] = (letterCounts[ch] ?? 0) + 1;
		const tileList = tilesBag[ch.charCodeAt(0) - 65];
		const startIdx = letterCounts[ch] - 1; // the kth occurrence uses tileList[k]
		for (let j = 0; j < n; j++) M[i][j] = 999999; // init
		// assign this occurrence to each tile instance
		let col = 0;
		for (let tIdx = startIdx; tIdx < tileList.length; tIdx++) {
			const tile = tileList[tIdx];
			const letterValue = LETTER_SCORES[ch.charCodeAt(0) - 65] * tile.letterMult;
			const wordMult = tile.wordMult;
			// cost = - (letterValue)   we handle word multipliers later
			M[i][col++] = -letterValue - (wordMult > 1 ? 1e4 * wordMult : 0); // cheap trick to prioritise DW/TW
		}
	}

	const result = solveAssignment(M); // returns assignment result
	const assign = result.assignment; // [row->col] array

	// Check if assignment was successful
	if (!result.success || assign.length !== n) {
		// Fallback: use base letter scores without multipliers
		return letterScoreSum;
	}

	let letterSum = 0,
		wordMultTotal = 1;
	for (let r = 0; r < n; r++) {
		const letterIdx = word[r].charCodeAt(0) - 65;
		const tileList = tilesBag[letterIdx];
		const assignedCol = assign[r];

		// Safety checks
		if (assignedCol < 0 || assignedCol >= tileList.length) {
			// Fallback to base score for this letter
			letterSum += LETTER_SCORES[letterIdx];
			continue;
		}

		const tile = tileList[assignedCol];
		if (!tile) {
			// Fallback to base score for this letter
			letterSum += LETTER_SCORES[letterIdx];
			continue;
		}

		letterSum += LETTER_SCORES[letterIdx] * tile.letterMult;
		wordMultTotal *= tile.wordMult;
	}
	return letterSum * wordMultTotal;
}

// ---- MAIN export -----------------------------------------------------------
export function bestWords(board: Board, K: number): Word[] {
	const bag = summariseBoard(board);
	const freqB = bag.freq;

	const candid: Word[] = [];
	let worstScore = 0;

	for (const meta of ENTRIES) {
		// 1) quick length reject
		if (meta.wordLen > 25) continue;

		// 2) histogram subset check
		let ok = true;
		for (let i = 0; i < 26; i++)
			if (meta.hist[i] > freqB[i]) {
				ok = false;
				break;
			}
		if (!ok) continue;

		// 3) decode UTF-8 only now (lazy)
		const word = textDec.decode(
			new Uint8Array(dictBytes.buffer, dictBytes.byteOffset + meta.off, meta.len)
		);

		// 4) compute best placement score
		const score = bestPlacementScore(word, bag.tiles, meta.letterScoreSum);
		if (score < worstScore) continue; // early reject if already worse than top-K worst

		// 5) keep in min-heap (array + insertion sort is fine for small K)
		// Create dummy positions for the word (will be filled properly when actually playing)
		const dummyPositions: Array<[number, number]> = [];
		for (let i = 0; i < word.length; i++) {
			dummyPositions.push([0, i]); // Dummy positions in first row
		}
		const wordObj = new Word(word, dummyPositions, score);
		candid.push(wordObj);
		candid.sort((a, b) => b.score - a.score);
		if (candid.length > K) candid.pop();
		worstScore = candid[candid.length - 1].score;
	}
	return candid;
}
