/**
 * Ultra-fast version of bestWords function
 *
 * Key optimizations:
 * 1. Avoid Hungarian algorithm entirely for initial filtering
 * 2. Use greedy assignment for fast approximation
 * 3. Only use Hungarian for final top candidates
 * 4. Pre-sorted dictionary by base score
 * 5. Early termination with score bounds
 */

import { readFileSync } from 'node:fs';
import { Tile } from './models/Tile';
import { Board } from './models/Board';
import { Word } from './models/Word';
import { solveAssignment } from './solver/hungarian';
import { join } from 'path';

const DICT_BIN = join(process.cwd(), 'src/lib/dict.bin');
const dictBytes = readFileSync(DICT_BIN);
const dv = new DataView(dictBytes.buffer, dictBytes.byteOffset);
const textDec = new TextDecoder();

// Letter scores
const LETTER_SCORES: number[] = Array(26).fill(0);
Object.assign(LETTER_SCORES, {
	0: 1,
	1: 4,
	2: 4,
	3: 2,
	4: 1,
	5: 4,
	6: 2,
	7: 4,
	8: 1,
	9: 10,
	10: 5,
	11: 1,
	12: 4,
	13: 2,
	14: 1,
	15: 3,
	16: 10,
	17: 1,
	18: 1,
	19: 1,
	20: 2,
	21: 5,
	22: 4,
	23: 8,
	24: 3,
	25: 10
});

// Dictionary entries
interface DictEntryMeta {
	off: number;
	len: number;
	letterScoreSum: number;
	wordLen: number;
	hist: Uint8Array;
}

let ENTRIES: DictEntryMeta[] | null = null;
let ENTRIES_BY_SCORE: DictEntryMeta[] | null = null;

function loadDictionary(): DictEntryMeta[] {
	if (ENTRIES) return ENTRIES;

	let o = 0;
	const nEntries = dv.getUint32(o, true);
	o += 4;
	const entries: DictEntryMeta[] = new Array(nEntries);

	for (let idx = 0; idx < nEntries; idx++) {
		const wordBytes = dv.getUint16(o, true);
		o += 2;
		const score = dv.getUint16(o, true);
		o += 2;
		const wlen = dv.getUint8(o);
		o += 1;
		const hist = new Uint8Array(dictBytes.buffer, dictBytes.byteOffset + o, 26);
		o += 26;

		entries[idx] = { off: o, len: wordBytes, letterScoreSum: score, wordLen: wlen, hist };
		o += wordBytes;
	}

	ENTRIES = entries;
	return entries;
}

function getEntriesByScore(): DictEntryMeta[] {
	if (ENTRIES_BY_SCORE) return ENTRIES_BY_SCORE;

	const entries = loadDictionary();
	ENTRIES_BY_SCORE = [...entries].sort((a, b) => b.letterScoreSum - a.letterScoreSum);
	return ENTRIES_BY_SCORE;
}

// Board summary
interface LetterBag {
	freq: Uint8Array;
	tiles: Tile[][];
	bestTiles: Tile[]; // Best tile for each letter (highest multiplier)
	maxMultipliers: { letter: number; word: number };
}

function summariseBoard(board: Board): LetterBag {
	const freq = new Uint8Array(26);
	const tiles: Tile[][] = Array.from({ length: 26 }, (_) => []);
	const bestTiles: Tile[] = new Array(26);
	let maxLetterMult = 1;
	let maxWordMult = 1;

	for (const row of board.tiles) {
		for (const t of row) {
			const idx = t.letter.charCodeAt(0) - 65;
			freq[idx]++;
			tiles[idx].push(t);

			// Track best tile for each letter
			if (
				!bestTiles[idx] ||
				t.letterMult * t.wordMult > bestTiles[idx].letterMult * bestTiles[idx].wordMult
			) {
				bestTiles[idx] = t;
			}

			maxLetterMult = Math.max(maxLetterMult, t.letterMult);
			maxWordMult = Math.max(maxWordMult, t.wordMult);
		}
	}

	return { freq, tiles, bestTiles, maxMultipliers: { letter: maxLetterMult, word: maxWordMult } };
}

// Ultra-fast greedy scoring (no Hungarian algorithm)
function greedyPlacementScore(word: string, bag: LetterBag): number {
	let totalScore = 0;
	let wordMultiplier = 1;
	const letterCounts: Record<string, number> = {};

	for (const ch of word) {
		const letterIdx = ch.charCodeAt(0) - 65;
		letterCounts[ch] = (letterCounts[ch] || 0) + 1;

		const availableTiles = bag.tiles[letterIdx];
		if (availableTiles.length >= letterCounts[ch]) {
			// Use the best available tile for this letter occurrence
			const tileIndex = Math.min(letterCounts[ch] - 1, availableTiles.length - 1);
			const tile = availableTiles[tileIndex];

			totalScore += LETTER_SCORES[letterIdx] * tile.letterMult;
			wordMultiplier *= tile.wordMult;
		} else {
			// Not enough tiles, use base score
			totalScore += LETTER_SCORES[letterIdx];
		}
	}

	return totalScore * wordMultiplier;
}

// Optimistic upper bound (maximum possible score)
function upperBoundScore(word: string, bag: LetterBag): number {
	let totalScore = 0;
	const letterCounts: Record<string, number> = {};

	for (const ch of word) {
		const letterIdx = ch.charCodeAt(0) - 65;
		letterCounts[ch] = (letterCounts[ch] || 0) + 1;

		// Use best possible tile for this letter
		const bestTile = bag.bestTiles[letterIdx];
		if (bestTile) {
			totalScore += LETTER_SCORES[letterIdx] * bestTile.letterMult;
		} else {
			totalScore += LETTER_SCORES[letterIdx];
		}
	}

	// Apply maximum word multiplier
	return totalScore * bag.maxMultipliers.word;
}

// Precise Hungarian scoring (only for final candidates)
function precisePlacementScore(word: string, tilesBag: Tile[][], letterScoreSum: number): number {
	const n = word.length;
	if (n > 12) return letterScoreSum; // Skip Hungarian for very long words

	const M = Array.from<number[], number>({ length: n }, () => new Array(n).fill(999999));

	const letterCounts: Record<string, number> = {};
	for (let i = 0; i < n; i++) {
		const ch = word[i];
		letterCounts[ch] = (letterCounts[ch] ?? 0) + 1;
		const tileList = tilesBag[ch.charCodeAt(0) - 65];
		const startIdx = letterCounts[ch] - 1;

		let col = 0;
		for (let tIdx = startIdx; tIdx < Math.min(tileList.length, n); tIdx++) {
			const tile = tileList[tIdx];
			const letterValue = LETTER_SCORES[ch.charCodeAt(0) - 65] * tile.letterMult;
			const wordMult = tile.wordMult;
			M[i][col++] = -letterValue - (wordMult > 1 ? 1e4 * wordMult : 0);
		}
	}

	const result = solveAssignment(M);
	if (!result.success || result.assignment.length !== n) {
		return letterScoreSum;
	}

	let letterSum = 0,
		wordMultTotal = 1;
	for (let r = 0; r < n; r++) {
		const letterIdx = word[r].charCodeAt(0) - 65;
		const tileList = tilesBag[letterIdx];
		const assignedCol = result.assignment[r];

		if (assignedCol < 0 || assignedCol >= tileList.length) {
			letterSum += LETTER_SCORES[letterIdx];
			continue;
		}

		const tile = tileList[assignedCol];
		if (!tile) {
			letterSum += LETTER_SCORES[letterIdx];
			continue;
		}

		letterSum += LETTER_SCORES[letterIdx] * tile.letterMult;
		wordMultTotal *= tile.wordMult;
	}
	return letterSum * wordMultTotal;
}

// Find valid positions for a word on the board
function findWordPositions(word: string, board: Board): Array<[number, number]> | null {
	// Simple greedy placement - find any valid path
	const tiles = board.getAllTiles();
	const letters = word.split('');

	// Try to find a path starting from each tile
	for (const startTile of tiles) {
		if (startTile.letter === letters[0]) {
			const path = findWordPath(letters, board, startTile.row, startTile.col, []);
			if (path) {
				return path;
			}
		}
	}

	// Fallback: return dummy positions if no valid path found
	return letters.map((_, i) => [0, i] as [number, number]);
}

// Recursively find a path that spells the given letters
function findWordPath(
	letters: string[],
	board: Board,
	row: number,
	col: number,
	usedPositions: Array<[number, number]>
): Array<[number, number]> | null {
	if (letters.length === 0) {
		return [];
	}

	const tile = board.getTile(row, col);
	if (!tile || tile.letter !== letters[0]) {
		return null;
	}

	// Check if position already used
	if (usedPositions.some(([r, c]) => r === row && c === col)) {
		return null;
	}

	if (letters.length === 1) {
		return [[row, col]];
	}

	// Try adjacent positions
	const adjacent = board.getAdjacentTiles(row, col);
	for (const adjTile of adjacent) {
		const path = findWordPath(letters.slice(1), board, adjTile.row, adjTile.col, [
			...usedPositions,
			[row, col]
		]);

		if (path) {
			return [[row, col], ...path];
		}
	}

	return null;
}

// Ultra-fast main function
export function bestWordsFast(board: Board, K: number): Word[] {
	const bag = summariseBoard(board);
	const freqB = bag.freq;

	// Phase 1: Fast filtering with greedy scoring
	const candidates: { word: string; score: number; meta: DictEntryMeta }[] = [];
	let minScore = 0;

	// Use pre-sorted entries by base score for better early termination
	const entries = getEntriesByScore();
	let processedCount = 0;
	const maxProcessed = Math.min(50000, entries.length); // Limit processing for speed

	for (const meta of entries) {
		if (processedCount++ > maxProcessed) break;

		// Quick length reject
		if (meta.wordLen > 25) continue;

		// Early termination if base score is too low
		if (meta.letterScoreSum * bag.maxMultipliers.letter * bag.maxMultipliers.word < minScore) {
			break; // Since entries are sorted by score, we can stop here
		}

		// Histogram subset check
		let ok = true;
		for (let i = 0; i < 26; i++) {
			if (meta.hist[i] > freqB[i]) {
				ok = false;
				break;
			}
		}
		if (!ok) continue;

		// Decode word
		const word = textDec.decode(
			new Uint8Array(dictBytes.buffer, dictBytes.byteOffset + meta.off, meta.len)
		);

		// Upper bound check
		const upperBound = upperBoundScore(word, bag);
		if (upperBound < minScore) continue;

		// Greedy scoring
		const greedyScore = greedyPlacementScore(word, bag);
		if (greedyScore < minScore) continue;

		candidates.push({ word, score: greedyScore, meta });

		// Keep only top K candidates
		if (candidates.length > K * 2) {
			// Keep 2x for safety
			candidates.sort((a, b) => b.score - a.score);
			candidates.length = K;
			minScore = candidates[candidates.length - 1].score;
		}
	}

	// Sort candidates by greedy score
	candidates.sort((a, b) => b.score - a.score);
	const topCandidates = candidates.slice(0, Math.min(K * 2, candidates.length));

	// Phase 2: Precise scoring for top candidates only
	const finalResults: Word[] = [];

	for (const candidate of topCandidates) {
		const preciseScore = precisePlacementScore(
			candidate.word,
			bag.tiles,
			candidate.meta.letterScoreSum
		);

		// Find actual positions for the word on the board
		const positions = findWordPositions(candidate.word, board);
		if (positions) {
			const wordObj = new Word(candidate.word, positions, preciseScore);
			finalResults.push(wordObj);
		}
	}

	// Final sort and trim
	finalResults.sort((a, b) => b.score - a.score);
	return finalResults.slice(0, K);
}
